## This repository is a submodule for sharing types and utils across MGIS client applications ( Whitelabel and Mobile)
Configured in [here](../../.gitmodules)

## Update

To update the submodule, run the following command in the root of your project:

```bash
git submodule update --remote --merge 
```

## Editing
To edit the submodule, navigate to the submodule directory and make your changes. After making changes, commit them in the submodule's directory:

```bash
git add .
git commit -m "..."
git push
```
