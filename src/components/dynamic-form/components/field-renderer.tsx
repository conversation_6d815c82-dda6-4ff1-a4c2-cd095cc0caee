'use client'

import React from 'react'

import { FieldPath, FieldValues } from 'react-hook-form'

import { cn } from '@/lib/utils'
import {
  convertToMultiSelectOptions,
  FormCalendar,
  FormCheckbox,
  FormCountrySelect,
  FormInput,
  FormMultiSelect,
  FormPhoneInput,
  FormSelect,
  FormTextarea,
  FormUpload,
  SelectOption,
} from '@/components/form-inputs'

import { FieldConfig, FieldRendererProps } from '../types'

/**
 * FieldRenderer component that maps JSON field configurations to existing form input components
 *
 * This component acts as a mapping layer between JSON configuration and the existing
 * form components from src/components/form-inputs/
 */
export function FieldRenderer<TFieldValues extends FieldValues = FieldValues>({
  field,
  form,
  disabled = false,
  className,
  sectionId,
  callbacks: _callbacks,
  stepId: _stepId,
}: FieldRendererProps<TFieldValues>) {
  // Determine field name - use nested path if sectionId provided
  const fieldName = sectionId ? `${sectionId}.${field.name}` : field.name

  // Common props that apply to most field types
  const commonProps = {
    name: fieldName as FieldPath<TFieldValues>,
    label: field.label,
    isRequired: field.isRequired,
    placeholder: field.placeholder,
    disabled: disabled || field.disabled,
    className: cn(getGridColumnClass(field.colSpan || 1), className),
  }

  // Render field based on type
  switch (field.type) {
    case 'input':
      return <FormInput<TFieldValues> {...commonProps} type="text" suffix={field.suffix} />

    case 'textarea':
      return <FormTextarea<TFieldValues> {...commonProps} maxLength={field.maxLength} showCounter={field.showCounter} />

    case 'select':
      return (
        <FormSelect<TFieldValues>
          {...commonProps}
          options={field.options || []}
          autoComplete={!!field.autoComplete}
          searchPlaceholder={field.searchPlaceholder}
          emptyMessage={field.emptyMessage}
        />
      )

    case 'multiselect':
      return (
        <FormMultiSelect<TFieldValues>
          {...commonProps}
          options={convertToMultiSelectOptions(field.options || [])}
          maxCount={field.maxCount || 3}
          animation={0}
          modalPopover={false}
          variant="default"
        />
      )

    case 'checkbox':
      return (
        <FormCheckbox<TFieldValues>
          name={field.name as FieldPath<TFieldValues>}
          label={field.label}
          control={form.control}
          disabled={disabled || field.disabled}
          description={field.helperText}
        />
      )

    case 'radio':
      // For radio groups, we'll use FormSelect with single selection
      // TODO: Consider creating a dedicated FormRadioGroup component
      return <FormSelect<TFieldValues> {...commonProps} options={field.options || []} autoComplete={false} />

    case 'switch':
      // For now, we'll use checkbox as switch - could be enhanced with a dedicated Switch component
      return (
        <FormCheckbox<TFieldValues>
          name={field.name as FieldPath<TFieldValues>}
          label={field.label}
          control={form.control}
          disabled={disabled || field.disabled}
          description={field.helperText}
        />
      )

    case 'date':
      return <FormCalendar<TFieldValues> {...commonProps} dateOnly={true} includeTime={false} />

    case 'datetime':
      return <FormCalendar<TFieldValues> {...commonProps} includeTime={true} includeSeconds={field.includeSeconds} />

    case 'month':
      return (
        <FormCalendar<TFieldValues>
          {...commonProps}
          dateOnly={false}
          includeTime={false}
          // TODO: Add month-only mode to FormCalendar
        />
      )

    case 'phone':
      return <FormPhoneInput<TFieldValues> {...commonProps} />

    case 'country':
      return (
        <FormCountrySelect<TFieldValues>
          {...commonProps}
          autoComplete={field.autoComplete === null ? true : !!field.autoComplete}
          searchPlaceholder={field.searchPlaceholder}
          emptyMessage={field.emptyMessage}
          isNationality={field.isNationality}
        />
      )

    case 'upload':
      return (
        <FormUpload<TFieldValues>
          {...commonProps}
          accept={field.accept}
          multiple={field.multiple}
          maxFiles={field.maxFiles}
          maxSize={field.maxSize}
          description={field.helperText}
        />
      )

    default:
      console.warn(`Unsupported field type: ${field.type}`)
      return (
        <div className={cn(getGridColumnClass(field.colSpan || 1), className)}>
          <p className="text-sm text-muted-foreground">Unsupported field type: {field.type}</p>
        </div>
      )
  }
}

/**
 * Generate CSS class for grid column span
 */
function getGridColumnClass(colSpan: number): string {
  const spanClasses: Record<number, string> = {
    1: 'col-span-1',
    2: 'col-span-2',
    3: 'col-span-3',
    4: 'col-span-4',
    5: 'col-span-5',
    6: 'col-span-6',
    7: 'col-span-7',
    8: 'col-span-8',
    9: 'col-span-9',
    10: 'col-span-10',
    11: 'col-span-11',
    12: 'col-span-12',
  }

  return spanClasses[colSpan] || 'col-span-1'
}

/**
 * Convert FieldConfig options to SelectOption format
 */
export function convertToSelectOptions(options: Array<{ value: string; label: string }> = []): SelectOption[] {
  return options.map((option) => ({
    value: option.value,
    label: option.label,
  }))
}

/**
 * Get default value for a field based on its type
 */
export function getFieldDefaultValue(field: FieldConfig): unknown {
  if (field.defaultValue !== undefined) {
    return field.defaultValue
  }

  switch (field.type) {
    case 'input':
    case 'textarea':
      return ''
    case 'select':
    case 'country':
    case 'phone':
      return ''
    case 'multiselect':
      return []
    case 'checkbox':
    case 'switch':
      return false
    case 'date':
    case 'datetime':
    case 'month':
      return undefined
    case 'upload':
      return undefined
    default:
      return undefined
  }
}

/**
 * Validate field configuration
 */
export function validateFieldConfig(field: FieldConfig): string[] {
  const errors: string[] = []

  // Check required properties
  if (!field.name) {
    errors.push('Field name is required')
  }

  if (!field.label) {
    errors.push('Field label is required')
  }

  if (!field.type) {
    errors.push('Field type is required')
  }

  // Type-specific validation
  if (['select', 'multiselect', 'radio'].includes(field.type) && (!field.options || field.options.length === 0)) {
    errors.push(`Field type '${field.type}' requires options`)
  }

  if (field.colSpan && (field.colSpan < 1 || field.colSpan > 12)) {
    errors.push('colSpan must be between 1 and 12')
  }

  return errors
}
