'use client'

import React from 'react'

import { DynamicForm, DynamicFormConfig, FormStepperCallbacks } from '../index'

/**
 * Example demonstrating the enhanced DynamicForm with dynamic-array field types
 *
 * This example shows how to use the new dynamic-array field type to handle
 * complex arrays of objects like contracts and payment methods.
 */

const exampleConfig: DynamicFormConfig = {
  title: 'Enhanced Dynamic Form Example',
  description: 'Demonstrating dynamic arrays with embedded components',
  submitButtonText: 'Complete Setup',
  nextButtonText: 'Continue',
  previousButtonText: 'Back',
  showStepNumbers: true,
  showProgressBar: true,
  allowStepNavigation: false,
  validateOnStepChange: true,
  steps: [
    {
      id: 'personal',
      title: 'Personal Information',
      description: 'Basic information about yourself',
      columns: 2,
      fields: [
        {
          name: 'firstName',
          type: 'input',
          label: 'First Name',
          isRequired: true,
          colSpan: 1,
          validation: [
            { type: 'required', message: 'First name is required' },
            { type: 'minLength', value: 2, message: 'Minimum 2 characters' },
          ],
        },
        {
          name: 'lastName',
          type: 'input',
          label: 'Last Name',
          isRequired: true,
          colSpan: 1,
          validation: [
            { type: 'required', message: 'Last name is required' },
            { type: 'minLength', value: 2, message: 'Minimum 2 characters' },
          ],
        },
        {
          name: 'email',
          type: 'input',
          label: 'Email Address',
          isRequired: true,
          colSpan: 2,
          validation: [
            { type: 'required', message: 'Email is required' },
            { type: 'email', message: 'Invalid email format' },
          ],
        },
      ],
    },
    {
      id: 'contracts',
      title: 'Contract Management',
      description: 'Manage your contracts and agreements',
      columns: 1,
      fields: [
        {
          name: 'contracts',
          type: 'dynamic-array',
          label: 'Your Contracts',
          helperText: 'Add and manage your insurance contracts',
          isRequired: false,
          colSpan: 1,
          componentName: 'contracts',
          arrayConfig: {
            minItems: 0,
            maxItems: 5,
            addButtonText: 'Add New Contract',
            removeButtonText: 'Remove Contract',
            emptyStateText: 'No contracts added yet. Click "Add New Contract" to get started.',
            itemLabelField: 'name',
          },
        },
      ],
    },
    {
      id: 'payment',
      title: 'Payment Methods',
      description: 'Set up your payment preferences',
      columns: 1,
      fields: [
        {
          name: 'paymentMethods',
          type: 'dynamic-array',
          label: 'Payment Methods',
          helperText: 'Add at least one payment method for your account',
          isRequired: true,
          colSpan: 1,
          componentName: 'payment-methods',
          arrayConfig: {
            minItems: 1,
            maxItems: 3,
            addButtonText: 'Add Payment Method',
            removeButtonText: 'Remove Payment Method',
            emptyStateText: 'No payment methods configured. Please add at least one payment method.',
            itemLabelField: 'type',
          },
          validation: [{ type: 'required', message: 'At least one payment method is required' }],
        },
      ],
    },
    {
      id: 'review',
      title: 'Review & Confirm',
      description: 'Review your information before submitting',
      columns: 1,
      fields: [
        {
          name: 'terms',
          type: 'checkbox',
          label: 'I agree to the terms and conditions',
          isRequired: true,
          colSpan: 1,
          validation: [{ type: 'isTrue', message: 'You must agree to the terms and conditions' }],
        },
        {
          name: 'newsletter',
          type: 'checkbox',
          label: 'Subscribe to newsletter for updates',
          isRequired: false,
          colSpan: 1,
        },
      ],
    },
  ],
}

export function DynamicArrayExample() {
  const callbacks: FormStepperCallbacks = {
    onSubmit: async (data) => {
      console.log('Form submitted with data:', data)

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      alert('Form submitted successfully! Check console for data.')
    },
    onStepChange: async ({ currentStepData, currentStepId, step, direction }) => {
      console.log('Step changed:', {
        stepId: currentStepId,
        step,
        direction,
        data: currentStepData,
      })

      // You can perform validation or data processing here
      // Return false to prevent step change if needed
      return true
    },
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">Enhanced DynamicForm Example</h1>
        <p className="text-gray-600 mb-4">
          This example demonstrates the new dynamic-array field type that can handle complex arrays of objects with
          embedded React components.
        </p>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h3 className="font-semibold text-blue-900 mb-2">Key Features Demonstrated:</h3>
          <ul className="text-blue-800 text-sm space-y-1">
            <li>• Dynamic array fields with add/remove functionality</li>
            <li>• Embedded React components (contracts and payment methods)</li>
            <li>• Array validation (min/max items, required arrays)</li>
            <li>• Integration with React Hook Form's useFieldArray</li>
            <li>• Flexible configuration for different component types</li>
          </ul>
        </div>
      </div>

      <DynamicForm config={exampleConfig} callbacks={callbacks} className="bg-white shadow-lg rounded-lg" />

      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold mb-2">Configuration Structure:</h3>
        <pre className="text-xs bg-white p-3 rounded border overflow-x-auto">
          {`{
  "name": "contracts",
  "type": "dynamic-array",
  "label": "Your Contracts",
  "componentName": "contracts",
  "arrayConfig": {
    "minItems": 0,
    "maxItems": 5,
    "addButtonText": "Add New Contract",
    "removeButtonText": "Remove Contract",
    "emptyStateText": "No contracts added yet...",
    "itemLabelField": "name"
  }
}`}
        </pre>
      </div>
    </div>
  )
}
