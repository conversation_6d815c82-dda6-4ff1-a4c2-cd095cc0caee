// Additional utility types for component props
import { <PERSON>Val<PERSON>, UseFormReturn } from 'react-hook-form'

import {
  ComplexCondition,
  Condition,
  DynamicFormConfig,
  FieldConfig,
  FormData,
  FormStepperCallbacks,
  FormStepperConfig,
  FormStepperState,
  StepConfig,
  ValidationRule,
} from './form-stepper-schema'
// Import specific types for use in interfaces
import type { StepDependencies } from './form-stepper-schema'

// Re-export all types and schemas from form-stepper-schema.ts
export * from './form-stepper-schema'

// Props for the main DynamicForm component
export interface DynamicFormProps {
  config: DynamicFormConfig
  callbacks: FormStepperCallbacks
  className?: string
  initialData?: Partial<FormData>
  disabled?: boolean
  navigation?: React.ComponentType<NavigationProps> // Custom navigation component
  stepDependencies?: StepDependencies // External dependencies for specialized steps
}

// Props passed to custom navigation component
export interface NavigationProps {
  currentStep: number | string
  totalSteps: number
  nextStepDisabled: boolean
  submitButtonDisabled: boolean
  previousStepDisabled: boolean
  isFirstStep: boolean
  isLastStep: boolean
  isSubmitting: boolean

  showSubmitButton: boolean
  onNext: () => void
  onPrevious: () => void
  onSubmit: () => void
  nextButtonText?: string
  previousButtonText?: string
  submitButtonText?: string
}

// Legacy alias for backward compatibility
export interface DynamicFormStepperProps {
  config: FormStepperConfig
  callbacks: FormStepperCallbacks
  className?: string
  initialData?: Partial<FormData>
  disabled?: boolean
}

// Props for FieldRenderer component
export interface FieldRendererProps<TFieldValues extends FieldValues = FieldValues> {
  field: FieldConfig
  form: UseFormReturn<TFieldValues>
  disabled?: boolean
  className?: string
  sectionId?: string // For nested form structure
  callbacks?: FormStepperCallbacks
  stepId?: string
}

// Props for StepperNavigation component
export interface StepperNavigationProps {
  steps: StepConfig[]
  currentStep: number
  completedSteps: Set<number>
  onStepChange: (step: number) => void
  allowStepNavigation: boolean
  showStepNumbers: boolean
  className?: string
}

// Props for individual step components
export interface FormStepProps<TFieldValues extends FieldValues = FieldValues> {
  step: StepConfig
  form: UseFormReturn<TFieldValues>
  disabled?: boolean
  className?: string
}

// Validation context for dynamic schema generation
export interface ValidationContext {
  fieldName: string
  fieldType: string
  rules: ValidationRule[]
  isRequired: boolean
}

// Condition evaluation context
export interface ConditionEvaluationContext {
  formData: FormData
  fieldName: string
  condition: Condition | ComplexCondition
}

// Field mapping for FieldRenderer

export interface FieldComponentMapping {
  input: React.ComponentType<Record<string, unknown>>
  textarea: React.ComponentType<Record<string, unknown>>
  select: React.ComponentType<Record<string, unknown>>
  multiselect: React.ComponentType<Record<string, unknown>>
  checkbox: React.ComponentType<Record<string, unknown>>
  radio: React.ComponentType<Record<string, unknown>>
  switch: React.ComponentType<Record<string, unknown>>
  date: React.ComponentType<Record<string, unknown>>
  datetime: React.ComponentType<Record<string, unknown>>
  month: React.ComponentType<Record<string, unknown>>
  phone: React.ComponentType<Record<string, unknown>>
  country: React.ComponentType<Record<string, unknown>>
  upload: React.ComponentType<Record<string, unknown>>
}

// Error handling types
export interface FormStepperError {
  type: 'validation' | 'submission' | 'configuration'
  message: string
  field?: string
  step?: number
}

// Hook return types
export interface UseFormStepperReturn {
  state: FormStepperState
  actions: {
    nextStep: () => void
    previousStep: () => void
    goToStep: (step: number) => void
    submitForm: () => Promise<void>
    resetForm: () => void
  }
  validation: {
    validateCurrentStep: () => Promise<boolean>
    validateAllSteps: () => Promise<boolean>
    getStepErrors: (step: number) => Record<string, string[]>
  }
}

export interface UseConditionalFieldsReturn {
  isFieldVisible: (fieldName: string) => boolean
  evaluateCondition: (condition: Condition | ComplexCondition) => boolean
  getVisibleFields: (fields: FieldConfig[]) => FieldConfig[]
}

export interface UseDynamicValidationReturn {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  generateSchema: (fields: FieldConfig[]) => any // Zod schema
  validateStep: (stepFields: FieldConfig[], data: FormData) => Promise<boolean>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  getFieldValidation: (field: FieldConfig) => any // Zod field schema
}

// Configuration validation types
export interface ConfigValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

// Grid layout types
export interface GridLayoutConfig {
  columns: number
  gap?: string
  className?: string
}

export interface FieldLayoutProps {
  colSpan: number
  totalColumns: number
  className?: string
}

// Theme and styling types
export interface FormStepperTheme {
  colors: {
    primary: string
    secondary: string
    success: string
    error: string
    warning: string
    info: string
  }
  spacing: {
    xs: string
    sm: string
    md: string
    lg: string
    xl: string
  }
  borderRadius: string
  fontSize: {
    xs: string
    sm: string
    md: string
    lg: string
    xl: string
  }
}

// Animation and transition types
export interface StepTransitionConfig {
  duration: number
  easing: string
  direction: 'horizontal' | 'vertical' | 'fade'
}

// Accessibility types
export interface AccessibilityConfig {
  announceStepChanges: boolean
  focusManagement: boolean
  keyboardNavigation: boolean
  screenReaderSupport: boolean
}

// Performance optimization types
export interface PerformanceConfig {
  lazyLoadSteps: boolean
  debounceValidation: number
  memoizeFields: boolean
}

// Export utility type helpers
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
