'use client'

import { DATA_MANAGEMENT_FORM_CONFIG } from '@/shared-fe/data-managment/data-managment-dynamic-form'

import { Card, CardContent } from '@/components/ui/card'
import { DynamicForm, DynamicFormConfig, FormStepperCallbacks } from '@/components/dynamic-form'

export function DataManagementDynamicForm() {
  const multiStepCallbacks: FormStepperCallbacks = {
    onSubmit: async (data) => {
      console.log('Form submitted:', data)
      // Handle final form submission
    },
    onStepChange: async ({ currentStepData, currentStepId, step, direction }) => {
      console.log('Step changed:', currentStepData, currentStepId, step, direction)
      return true
    },
  }
  return (
    <Card className="w-full">
      <CardContent>
        <DynamicForm config={DATA_MANAGEMENT_FORM_CONFIG} callbacks={multiStepCallbacks} />
      </CardContent>
    </Card>
  )
}
